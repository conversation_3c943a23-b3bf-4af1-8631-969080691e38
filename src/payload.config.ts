// storage-adapter-import-placeholder
import { mongooseAdapter } from '@payloadcms/db-mongodb';
import { nodemailerAdapter } from '@payloadcms/email-nodemailer';
import { payloadCloudPlugin } from '@payloadcms/payload-cloud';
import { lexicalEditor } from '@payloadcms/richtext-lexical';
import { s3Storage } from '@payloadcms/storage-s3';
import path from 'path';
import { buildConfig } from 'payload';
import sharp from 'sharp';
import { fileURLToPath } from 'url';
import { Icon, Logo } from './admin/components/Logo';
import aboutUsContent from './collections/AboutUsUi';
import appUsers from './collections/appUsers';
import browseInnovationContent from './collections/BrowseInnovationUi';
import ContactPage from './collections/ContactUsUi';
import { EmailTemplates } from './collections/EmailtTemplates';
import FAQContent from './collections/FaqUi';
import Flowchart from './collections/FlowChart';
import FooterContent from './collections/FooterContent';
import FormSelectOptions from './collections/FormOptions';
import InnovationClass from './collections/innovationClass';
import Innovations from './collections/Innovations';
import Inquiries from './collections/Inquiries';
import LandingPageContent from './collections/LandingPageUi';
import mapOfInnovationContent from './collections/MapOfInnovationsUi';
import { ProjectVersions } from './collections/projects';
import SubmitCriteria from './collections/SubmitCriteria';
import submitInnovationContent from './collections/SubmitInnovationUi';
import { Sunburst } from './collections/Sunburst';
import { Upload } from './collections/Upload';
import { Users } from './collections/Users';
const filename = fileURLToPath(import.meta.url);
const dirname = path.dirname(filename);

export default buildConfig({
  admin: {
    user: Users.slug,
    components: {
      beforeLogin: [],
      graphics: {
        Logo: Logo,
        Icon: Icon,
      },
    },
    meta: {
      titleSuffix: '- Atlas of Innovations',
      description: 'Welcome to India Innovation Summit - Pioneering solutions to End TB',
      applicationName: 'Atlas of Innovations',
      icons: [
        {
          rel: 'icon',
          type: 'image/png',
          url: 'images/icons/Atlas-of-Innovators.png',
        },
      ],
    },
    importMap: {
      baseDir: path.resolve(dirname),
    },
  },
  cors: ['https://atlasoftbinnovations.in'],
  email: nodemailerAdapter({
    defaultFromAddress: process.env.SMTP_FROM || '',
    defaultFromName: 'Atlas of Innovations',
    transportOptions: {
      host: process.env.SMTP_HOST,
      port: 465,
      secure: true,
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS,
      },
    },
  }),
  globals: [
    submitInnovationContent,
    browseInnovationContent,
    LandingPageContent,
    FooterContent,
    aboutUsContent,
    ContactPage,
    FAQContent,
    mapOfInnovationContent,
  ],
  collections: [
    Users,
    appUsers,
    SubmitCriteria,
    Innovations,
    Upload,
    FormSelectOptions,
    Flowchart,
    Sunburst,
    Inquiries,
    ProjectVersions,
    InnovationClass,
    EmailTemplates,
  ],
  editor: lexicalEditor(),
  secret: process.env.PAYLOAD_SECRET || '',
  typescript: {
    outputFile: path.resolve(dirname, 'payload-types.ts'),
  },
  db: mongooseAdapter({
    url: process.env.DATABASE_URI || '',
    // Enable transactions for better performance with complex operations
    transactionOptions: {
      readPreference: 'primary',
      readConcern: { level: 'local' },
      writeConcern: { w: 'majority' },
    },
    // Add connection pool settings for better performance
    connectOptions: {
      maxPoolSize: 10,
      minPoolSize: 5,
      maxIdleTimeMS: 30000,
      socketTimeoutMS: 30000,
    },
  }),
  sharp,
  upload: {
    limits: {
      fileSize: 6000000, // 6MB, written in bytes
    },
  },
  plugins: [
    payloadCloudPlugin(),
    s3Storage({
      collections: {
        upload: { disableLocalStorage: true },
      },
      bucket: process.env._AWS_BUCKET_NAME || '',
      config: {
        region: process.env._AWS_REGION || '',
        credentials: {
          accessKeyId: process.env._AWS_ACCESS_KEY_ID || '',
          secretAccessKey: process.env._AWS_SECRET_ACCESS_KEY || '',
        },
        // Optimize S3 client configuration
        maxAttempts: 3,
        retryMode: 'standard',
        // Reduce logging to improve performance
        logger: {
          debug: () => {}, // Disable debug logs
          error: console.error,
          info: () => {}, // Disable info logs
          warn: console.warn,
        },
      },
    }),
  ],
});
