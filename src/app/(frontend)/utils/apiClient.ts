'use server';
import config from '@payload-config';
import { getPayload } from 'payload';
import { API_ENDPOINTS } from '../constants/routes';
import {
  aboutUsContent,
  FlowItem,
  Innovation,
  LearnMoreHero,
  Option,
  SubmitContentType,
  ValidationError,
} from '../types/data.types';

export const checkAuth = async (): Promise<boolean> => {
  try {
    const res = await fetch(API_ENDPOINTS.ME, { credentials: 'include' });
    const data = await res.json();
    return !!data.user;
  } catch (error) {
    console.error('Auth check failed:', error);
    return false;
  }
};

export const fetchSubmitInnovationContent = async (): Promise<SubmitContentType | null> => {
  try {
    // Add depth parameter to populate the criteria relationship
    const res = await fetch(`${API_ENDPOINTS.SUBMIT_INNOVATION_CONTENT}?depth=1`);
    if (!res.ok) throw new Error('Failed to fetch UI content');
    return await res.json();
  } catch (error) {
    console.error(error);
    return null;
  }
};

export const fetchCriteriaContent = async () => {
  try {
    const res = await fetch(API_ENDPOINTS.SUBMIT_CRITERIA);
    if (!res.ok) throw new Error('Failed to fetch criteria content');
    return await res.json();
  } catch (error) {
    console.error(error);
    return null;
  }
};

export const fetchAboutUsContent = async (): Promise<aboutUsContent | null> => {
  try {
    const res = await fetch(API_ENDPOINTS.ABOUT_CONTENT);
    if (!res.ok) throw new Error('Failed to fetch UI content');
    return await res.json();
  } catch (error) {
    console.error(error);
    return null;
  }
};

export const submitInnovation = async (formData: any) => {
  try {
    const payload = await getPayload({ config });
    const response = await payload.create({
      collection: 'innovations',
      data: formData,
    });
    return response;
  } catch (err: ValidationError | any | unknown) {
    console.error('Submission error:', err.status, JSON.stringify(err));
    if (err?.status === 400) {
      // Check for specific validation errors
      const errors = err?.data?.errors || [];

      // Check for unique title constraint error
      const titleError = errors.find(
        (error: any) => error.path === 'title' && error.message.includes('unique'),
      );

      if (titleError) {
        return {
          code: 400,
          message: 'An innovation with this title already exists. Please use a different title.',
          response: errors,
        };
      }

      // Generic validation error
      return {
        code: 400,
        message: 'Please correct the validation errors.',
        response: errors,
      };
    } else {
      return { code: 500, message: 'Failed to submit innovation' };
    }
  }
};

export const updateInnovationVersion = async (id: string, formData: any) => {
  try {
    // Get existing project
    const project = (await formData.findByID({
      collection: 'innovations',
      id,
    })) as any;

    const currentVersion = project?.versionNumber || 0;

    // Create new version
    const newVersion = await formData.create({
      collection: 'project-versions',
      data: {
        ...formData,
        innovation: id,
        status: 'pending',
        versionNumber: currentVersion + 1,
      },
      // context: { disableTransactions: true },
    });

    return new Response(JSON.stringify(newVersion), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });
  } catch (err) {
    console.error('Update error:', err);
    throw new Error(`Failed to update innovation with ID ${id}`);
  }
};

export const fetchFlowChart = async (): Promise<{ docs: FlowItem[] }> => {
  try {
    const payload = await getPayload({ config });
    const response = await payload.find({
      collection: 'flowchart',
      pagination: false,
      depth: 2,
    });

    return { docs: response.docs as any };
  } catch (error) {
    console.error('Error fetching UI content:', error);
    return { docs: [] };
  }
};

export const fetchFlowChartForInnovation = async ({
  source,
  destination,
}: {
  source: string;
  destination: string;
}): Promise<{ docs: FlowItem[] }> => {
  try {
    const response = await fetch(
      `${API_ENDPOINTS.FLOW}?source=${source}&destination=${destination}`,
    );
    const data = await response.json();

    return data;
  } catch (error) {
    console.error('Error fetching UI content:', error);
    return { docs: [] };
  }
};

const groupOptionsByField = (docs: any[]): Record<string, Option[]> => {
  return docs.reduce(
    (acc, option) => {
      const { fieldName } = option;
      if (!acc[fieldName]) acc[fieldName] = [];
      acc[fieldName].push({
        value: option.value,
        label: option.label,
        id: option.id,
        description: option.description ?? option.descriptionEditor,
        slug: option.slug,
      });
      return acc;
    },
    {} as Record<string, Option[]>,
  );
};

export const fetchInnovation = async (title: string): Promise<Innovation> => {
  try {
    const payload = await getPayload({ config });

    const [innovationRes, optionsRes] = await Promise.all([
      payload.find({
        where: { slug: { equals: title } },
        collection: 'innovations',
        depth: 1,
      }),
      payload.find({
        collection: 'form-select-options',
        pagination: false,
        where: { isActive: { equals: true } },
      }),
    ]);

    const groupedOptions = groupOptionsByField(optionsRes.docs);

    const data = (await innovationRes?.docs?.[0]) as any;
    const currentStageOfDevelopmentTaxonomy = groupedOptions?.currentStageOfDevelopment?.find(
      (option) => option.value === data.currentStageOfDevelopment?.value,
    )?.description;

    if (!data) return { id: '', slug: '', title: '' };

    const formatted: Innovation = {
      id: data.id,
      title: data.title,
      description: data.description,
      currentStageOfDevelopment: data.currentStageOfDevelopment?.value ?? null,
      currentStageOfDevelopmentSlug: data.currentStageOfDevelopment?.slug ?? null,
      currentStageOfDevelopmentTaxonomy: currentStageOfDevelopmentTaxonomy ?? '',
      category: data?.category?.map((c: any) => c.value) ?? [],
      status: data.status,
      name: data.name,
      email: data.email,
      phoneNo: data.phoneNo,
      link: data.link,
      supportingDocument: data.supportingDocument,
      organizationName: data.organizationName,
      interventionType:
        data.interventionType?.map((t: any) => {
          return { title: t?.value, slug: t?.slug ?? null, description: t?.description ?? null };
        }) ?? [],
      bannerImage: data.bannerImage
        ? {
            id: data.bannerImage.id,
            name: data.bannerImage.filename ?? data.bannerImage.name,
          }
        : null,
      slug: data.slug,
      innovationClass: data.innovationClass
        ? {
            name: data.innovationClass.name,
            slug: data.innovationClass.slug,
          }
        : undefined,
    };

    return formatted;
  } catch (error) {
    console.error('Error fetching Innovation:', error);
    return { id: '', slug: '', title: '' };
  }
};

export const fetchOptions = async () => {
  try {
    const payload = await getPayload({ config });
    const response = await payload.find({
      collection: 'form-select-options',
      pagination: false,
      where: { isActive: { equals: true } },
    });
    return response;
  } catch (error) {
    console.error('Error fetching options:', error);
    return { docs: [] };
  }
};

export const fetchTop4FeaturedInnovations = async () => {
  try {
    const response = await fetch(API_ENDPOINTS.TOP_4);
    const data = await response.json();
    return Array.isArray(data?.docs) ? data?.docs : []; // ✅ Ensure it's always an array
  } catch (error) {
    console.error('Error fetching UI content:', error);
    return [];
  }
};

export const fetchTotalInnovations = async () => {
  try {
    const res = await fetch(API_ENDPOINTS.TOTAL_INNOVATION);
    if (!res.ok) throw new Error('Failed to fetch UI content(total count)');
    return await res.json();
  } catch (error) {
    console.error(error);
    return null;
  }
};

export const fetchLandingPageUi = async () => {
  try {
    const res = await fetch(`${process.env.NEXT_PUBLIC_BACKEND_URL}/api/globals/landingPageUi`);
    if (!res.ok) throw new Error('Failed to fetch UI content');
    const data = await res.json();
    return data;
  } catch (error) {
    console.error(error);
    return null;
  }
};

export async function fetchContsctUsUi() {
  try {
    const res = await fetch(
      `${process.env.NEXT_PUBLIC_BACKEND_URL}/api/globals/contactPage-content`,
    );

    if (!res.ok) {
      throw new Error('Failed to fetch contact page content');
    }

    return res.json();
  } catch (error) {
    console.error(error);
    return null;
  }
}

export const fetchLearnMoreUI = async (): Promise<LearnMoreHero | null> => {
  try {
    const res = await fetch(
      `${process.env.NEXT_PUBLIC_BACKEND_URL}/api/globals/mapOfInnovation-content`,
    );
    if (!res.ok) throw new Error('Failed to fetch UI content(learn more)');
    const data: LearnMoreHero = await res.json();
    return data;
  } catch (error) {
    console.error(error);
    return null;
  }
};

export const fetchSunburstUi = async () => {
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_BACKEND_URL}/api/sunburst/sunburst-data`,
      { cache: 'no-store' },
    );
    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }
    const jsonData = await response.json();
    return jsonData;
  } catch (error) {
    console.error('Error fetching innovations:', error);
  }
};

export async function fetchFaqUi() {
  const res = await fetch(
    `${process.env.NEXT_PUBLIC_BACKEND_URL}/api/globals/faq-content`,
    { cache: 'no-store' }, // Ensure fresh data on every request
  );

  if (!res.ok) {
    throw new Error('Failed to fetch FAQ data');
  }

  return res.json();
}

export const fetchInnovationClassBySlug = async (slug: string): Promise<any> => {
  try {
    const payload = await getPayload({ config });
    const res = await payload.find({
      collection: 'innovationClass',
      where: { slug: { equals: slug } },
      depth: 2, // Ensure relationships are populated
    });

    if (!res.docs.length) {
      return null;
    }

    const innovationClass: any = res.docs[0];

    // Format the response
    return {
      id: innovationClass.id,
      name: innovationClass.name,
      slug: innovationClass.slug,
      description: innovationClass.description,
      processFrom: innovationClass.processFrom,
      processTo: innovationClass.processTo,
      innovations:
        innovationClass.innovations?.map((innovation: any) => ({
          id: innovation.id || '',
          title: innovation.title || '',
          description: innovation.description || '',
          organizationName: innovation.organizationName || '',
          slug: innovation.slug || '',
          bannerImage: innovation.bannerImage || null,
          status: innovation.status || 'approved',
        })) || [],
    };
  } catch (error) {
    console.error('Error fetching Innovation Class:', error);
    return null;
  }
};

export const fetchInnovationClassByName = async (name: string): Promise<any> => {
  try {
    const payload = await getPayload({ config });
    const res = await payload.find({
      collection: 'innovationClass',
      where: { name: { equals: name } },
      depth: 2, // Ensure relationships are populated
    });

    if (!res.docs.length) {
      return null;
    }

    const innovationClass: any = res.docs[0];

    // Format the response
    return {
      id: innovationClass.id,
      name: innovationClass.name,
      slug: innovationClass.slug || '',
      description: innovationClass.description,
      processFrom: innovationClass.processFrom,
      processTo: innovationClass.processTo,
      innovations:
        innovationClass.innovations?.map((innovation: any) => ({
          id: innovation.id || '',
          title: innovation.title || '',
          description: innovation.description || '',
          organizationName: innovation.organizationName || '',
          slug: innovation.slug || '',
          bannerImage: innovation.bannerImage || null,
          status: innovation.status || 'approved',
        })) || [],
    };
  } catch (error) {
    console.error('Error fetching Innovation Class:', error);
    return null;
  }
};

export async function getTaxonomy(slug: string): Promise<any> {
  try {
    const payload = await getPayload({ config });
    const res = await payload.find({
      collection: 'form-select-options',
      pagination: false,
      where: { slug: { equals: slug } },
    });

    if (!res.docs.length) {
      return {
        value: 'NOT FOUND',
        description: '<h1><strong>Invalid url please check the url and try again.</strong></h1>',
      };
    }

    if (res?.docs?.[0]) {
      return res?.docs?.[0];
    }
  } catch (error) {
    console.error('Error fetching Innovation Class:', error);
    return null;
  }
}
