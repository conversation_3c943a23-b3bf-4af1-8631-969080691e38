'use client';

import Image from 'next/image';
import FlowChart from '../UI/FlowChart';

interface InnovationClassContentProps {
  innovationClass: any;
}

export default function InnovationClassContent({ innovationClass }: InnovationClassContentProps) {
  return (
    <>
      {innovationClass.description && (
        <div className="mt-8">
          <div className="flex items-center justify-between pb-2 mb-6 relative after:content-[''] after:absolute after:bottom-0 after:left-0 md:after:w-1/2 after:w-[90%] after:h-[3px] after:bg-orange-500">
            <h3 className="text-2xl font-bold text-blue-900">Description</h3>
            <Image
              src="/images/icons/about-svgrepo-com (1).svg"
              width={35}
              height={35}
              alt="About"
              className="absolute bottom-[-15px] md:left-[calc(50%)] left-[calc(90%)]"
            />
          </div>
          <div
            className="prose max-w-none"
            dangerouslySetInnerHTML={{ __html: innovationClass.description }}
          />
        </div>
      )}

      {innovationClass.processFrom && innovationClass.processTo && (
        <div className="mt-8">
          <div className="flex items-center justify-between pb-2 mb-6 relative after:content-[''] after:absolute after:bottom-0 after:left-0 md:after:w-1/2 after:w-[90%] after:h-[3px] after:bg-orange-500">
            <h3 className="text-2xl font-bold text-blue-900">Process Flow</h3>
            <Image
              src="/images/icons/category-svgrepo-com (1).svg"
              width={35}
              height={35}
              alt="Process Flow"
              className="absolute bottom-[-15px] md:left-[calc(50%)] left-[calc(90%)]"
            />
          </div>
          <div className="bg-white rounded-lg shadow-sm p-4 mb-8">
            <FlowChart
              currentInnovation={{
                processFrom: innovationClass.processFrom.id,
                processTo: innovationClass.processTo.id,
                innovationClassName: innovationClass.name,
                showChart: true,
              }}
            />
          </div>
        </div>
      )}
    </>
  );
}
