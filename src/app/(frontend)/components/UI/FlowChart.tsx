'use client';

import {
  Background,
  <PERSON>s,
  Edge,
  <PERSON>le,
  Node,
  Position,
  ReactFlow,
  useEdgesState,
  useNodesState,
} from '@xyflow/react';
import '@xyflow/react/dist/style.css';
import dagre from 'dagre';
import { useEffect, useState } from 'react';
import { FlowItem, LayoutElements } from '../../types/data.types';
import { fetchFlowChart, fetchFlowChartForInnovation } from '../../utils/apiClient';
import CircularLoader from './CircularLoader';

const nodeWidth = 200;
const nodeHeight = 80;

const getLayoutedElements = (nodes: Node[], edges: Edge[]): LayoutElements => {
  const g = new dagre.graphlib.Graph();
  g.setGraph({
    rankdir: 'TB',
    acyclicer: 'greedy',
    nodesep: 300,
    ranksep: 300,
  });
  g.setDefaultEdgeLabel(() => ({}));

  nodes.forEach((node) => g.setNode(node.id, { width: nodeWidth, height: nodeHeight }));
  edges.forEach((edge) => g.setEdge(edge.source, edge.target));

  dagre.layout(g);

  return {
    nodes: nodes.map((node) => {
      const graphNode = g.node(node.id);
      return {
        ...node,
        position: {
          x: graphNode?.x ?? node.position.x,
          y: graphNode?.y ?? node.position.y,
        },
      };
    }),
    edges,
  };
};

// Enhanced modern node component with improved UI
const CustomNode = ({ data }: { data: any }) => {
  return (
    <div className="group relative p-5 border border-gray-200 rounded-xl bg-white shadow-lg hover:shadow-xl transition-all duration-300 max-w-md">
      {/* Gradient border effect on hover */}
      <div
        className="absolute inset-0 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"
        style={{
          background: 'linear-gradient(to right, #6366f1, #8b5cf6, #d946ef)',
          padding: '1px',
          margin: '-1px',
          zIndex: -1,
        }}
      />

      {/* Node content with improved typography */}
      <div className="text-lg font-semibold text-gray-800 mb-1">{data.label}</div>

      {/* Handles with improved styling */}
      <Handle
        type="source"
        position={Position.Bottom}
        id="process-source"
        className="w-3 h-3 bg-indigo-500 border-2 border-white"
        style={{ bottom: -8, borderRadius: '50%' }}
      />
      <Handle
        type="target"
        position={Position.Top}
        id="process-target"
        className="w-3 h-3 bg-indigo-500 border-2 border-white"
        style={{ top: -8, borderRadius: '50%' }}
      />
      <Handle
        type="source"
        position={Position.Right}
        id="innovation-source"
        className="w-3 h-3 bg-purple-500 border-2 border-white"
        style={{ top: '50%', right: -8, borderRadius: '50%' }}
      />
      <Handle
        type="target"
        position={Position.Right}
        id="innovation-target"
        className="w-3 h-3 bg-purple-500 border-2 border-white"
        style={{ top: '50%', right: -8, borderRadius: '50%' }}
      />
    </div>
  );
};

const nodeTypes = {
  custom: CustomNode,
};

const Flowchart = ({ currentInnovation }: any) => {
  const [data, setData] = useState<FlowItem[] | null>(null);
  const [loading, setLoading] = useState(true);
  const [flowNodes, setFlowNodes] = useNodesState<any>([]);
  const [flowEdges, setFlowEdges] = useEdgesState<any>([]);

  useEffect(() => {
    const getData = async () => {
      try {
        const result = currentInnovation
          ? await fetchFlowChartForInnovation({
              source: currentInnovation.processFrom,
              destination: currentInnovation.processTo,
            })
          : await fetchFlowChart();

        if (result?.docs) {
          setData(result.docs);
        } else {
          console.error('No data found');
        }
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setLoading(false);
      }
    };
    getData();
  }, [currentInnovation]);

  useEffect(() => {
    if (!data || !Array.isArray(data) || !currentInnovation?.showChart) return;

    const nodeMap = new Map<string, string>();
    const nodesList: Node[] = [];
    const edgesList: Edge[] = [];

    const addNode = (id: string, name: string, nodeType: string = 'stage') => {
      if (!id || !name || nodeMap.has(id)) return;
      nodeMap.set(id, name);
      nodesList.push({
        id,
        type: 'custom',
        data: {
          label: name,
          // Add node type information for better display
          nodeType: nodeType,
        },
        position: { x: 0, y: 0 },
        // Modern styling is now handled in the CustomNode component
      });
    };

    const addEdge = (
      sourceId: string,
      targetId: string,
      label: string,
      strokeColor: string,
      type: string,
      sourceHandle?: string,
      targetHandle?: string,
    ) => {
      if (!sourceId || !targetId) return;
      edgesList.push({
        id: `${sourceId}-${targetId}-${type}-${edgesList.length}`,
        source: sourceId,
        target: targetId,
        sourceHandle: sourceHandle || 'parent-child-source',
        targetHandle: targetHandle || 'parent-child-target',
        animated: true,
        label: label.trim(),
        labelStyle: {
          fontSize: 20,
          fontWeight: 'normal',
        },
        style: {
          stroke: strokeColor,
          strokeWidth: type == 'innovation' ? 4 : 2,
        },
        type,
      });
    };

    data.forEach((item) => {
      if (item.type.toLowerCase() === 'stage') {
        addNode(item.id, item.name, 'stage');
        if (item.parent?.id) {
          addNode(item.parent.id, item.parent.name, 'parent');
          addEdge(
            item.parent.id,
            item.id,
            item.processName || '',
            '#6366f1', // Modern indigo color
            'bezier',
            'parent-child-source',
            'parent-child-target',
          );
        }
      }
    });

    data.forEach((item) => {
      if (item.type.toLowerCase() === 'process') {
        addNode(item.processFrom?.id as string, item.processFrom?.name as string, 'process-from');
        addNode(item.processTo?.id as string, item.processTo?.name as string, 'process-to');
        addEdge(
          item.processFrom?.id as string,
          item.processTo?.id as string,
          item.name,
          '#8b5cf6', // Modern purple color
          'process',
          'process-source',
          'process-target',
        );
      }
    });

    data.forEach((item) => {
      if (item.type.toLowerCase() === 'innovation') {
        addNode(
          item.processFrom?.id as string,
          item.processFrom?.name as string,
          'innovation-from',
        );
        addNode(item.processTo?.id as string, item.processTo?.name as string, 'innovation-to');
        addEdge(
          item.processFrom?.id as string,
          item.processTo?.id as string,
          currentInnovation.innovationClassName || currentInnovation?.value,
          '#FF0000',
          'innovation',
          'innovation-source',
          'innovation-target',
        );
      }
    });

    const { nodes: layoutedNodes, edges: layoutedEdges } = getLayoutedElements(
      nodesList,
      edgesList,
    );

    setFlowNodes(layoutedNodes);
    setFlowEdges(layoutedEdges);
  }, [data, setFlowEdges, setFlowNodes]);

  if (loading) {
    return <CircularLoader />;
  } else if (!data || !currentInnovation?.showChart) {
    return null;
  }

  // Define custom edge styles
  const edgeOptions = {
    animated: true,
    style: {
      stroke: '#6366f1',
      strokeWidth: 2,
    },
  };

  // Define custom connection line style
  const connectionLineStyle = {
    stroke: '#8b5cf6',
    strokeWidth: 2,
    strokeDasharray: '5,5',
  };

  return (
    <div
      className={`w-full ${currentInnovation?.isSmall ? 'h-[40vh] ' : 'h-[40vh] md:h-[40vh] lg:h-[706px] sm:h-[40vh]'}`}
    >
      {/* SVG definitions for markers and gradients */}
      <svg style={{ position: 'absolute', width: 0, height: 0 }}>
        <defs>
          <linearGradient id="edge-gradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor="#6366f1" />
            <stop offset="100%" stopColor="#d946ef" />
          </linearGradient>
          <marker
            id="edge-circle"
            viewBox="-5 -5 10 10"
            refX="0"
            refY="0"
            markerUnits="strokeWidth"
            markerWidth="10"
            markerHeight="10"
            orient="auto"
          >
            <circle stroke="#6366f1" strokeOpacity="0.75" r="2" fill="#fff" cx="0" cy="0" />
          </marker>
        </defs>
      </svg>

      <ReactFlow
        nodes={flowNodes}
        edges={flowEdges}
        nodeTypes={nodeTypes}
        fitView={true}
        fitViewOptions={{
          padding: 0.5,
          minZoom: 0.7,
          maxZoom: 1.5,
        }}
        defaultViewport={{ x: 0, y: 0, zoom: 1 }}
        proOptions={{ hideAttribution: true }}
        zoomOnScroll={false}
        zoomOnPinch={false}
        panOnScroll={false}
        preventScrolling={false}
        defaultEdgeOptions={edgeOptions}
        connectionLineStyle={connectionLineStyle}
        className="bg-white"
      >
        {/* Customized background with dots pattern */}
        <Background
          gap={20}
          size={1}
          color="#e5e7eb"
          lineWidth={2}
          style={{ backgroundColor: '#ffffff' }}
        />

        {/* Styled controls */}
        <Controls className="bg-white shadow-md rounded-md border border-gray-200" />
      </ReactFlow>
    </div>
  );
};

export default Flowchart;
