import { CollectionConfig } from 'payload';

const Flowchart: CollectionConfig = {
  slug: 'flowchart',
  admin: {
    useAsTitle: 'name',
    description: 'Data for flowchart visualizations',
    group: 'Visualization Data',
    hideAPIURL: true,
  },
  labels: {
    singular: 'Mindmap',
    plural: 'Mindmaps',
  },
  access: {
    read: () => true,
  },
  endpoints: [
    {
      path: '/flow',
      method: 'get',
      handler: async (req) => {
        const { source, destination }: any = req.query;
        if (!source || !destination) {
          return Response.json({ error: 'Missing source or destination' }, { status: 400 });
        }

        try {
          // Single formatting function
          const formatNode = (node: any) => {
            const formattedNode: any = {
              id: node.id,
              name: node.name,
              type: node.type,
              processName: node.processName,
              processFrom: node.processFrom
                ? { id: node.processFrom.id, name: node.processFrom.name }
                : node?.innovationclass?.processFrom
                  ? {
                      id: node.innovationclass.processFrom.id,
                      name: node.innovationclass.processFrom.name,
                    }
                  : null,
              processTo: node.processTo
                ? { id: node.processTo.id, name: node.processTo.name }
                : node?.innovationclass?.processTo
                  ? {
                      id: node.innovationclass.processTo.id,
                      name: node.innovationclass.processTo.name,
                    }
                  : null,
              createdAt: node.createdAt,
              updatedAt: node.updatedAt,
            };

            if (node.innovationclass) {
              formattedNode.innovationclass = {
                id: node.innovationclass.id,
                name: node.innovationclass.name,
                processFrom: node.innovationclass.processFrom,
                processTo: node.innovationclass.processTo,
                innovations: node.innovationclass.innovations,
              };
            }

            if (node.parent) {
              formattedNode.parent = {
                id: node.parent.id,
                name: node.parent.name,
                type: node.parent.type,
                createdAt: node.parent.createdAt,
                updatedAt: node.parent.updatedAt,
              };
            }

            return formattedNode;
          };

          // Batch fetch source and destination nodes
          const getNodesWithHierarchy = async (nodeIds: string[]) => {
            const nodes = await req.payload.find({
              collection: 'flowchart',
              where: { id: { in: nodeIds } },
              depth: 2,
            });
            return nodes.docs.reduce((map: any, node) => {
              map[node.id] = formatNode(node);
              return map;
            }, {});
          };

          // Helper function to find intermediate nodes in a hierarchical path
          const findIntermediateNodeIds = async (
            source: string,
            destination: string,
          ): Promise<string[]> => {
            // Fetch all stage nodes to build the complete hierarchy
            const { docs: allStageNodes } = await req.payload.find({
              collection: 'flowchart',
              where: { type: { equals: 'Stage' } },
              pagination: false,
            });

            // Build parent-child relationships map
            const parentChildMap = new Map<string, string>();
            allStageNodes.forEach((node: any) => {
              if (node.parent && typeof node.parent === 'object' && node.parent.id) {
                parentChildMap.set(node.id, node.parent.id);
              }
            });

            // Find path from destination up to source (or root)
            const pathNodeIds = new Set<string>();
            let current = destination;

            // Add cycle detection to prevent infinite loops
            const visitedNodes = new Set<string>();

            // Traverse up the hierarchy
            while (current && current !== source) {
              // Check for cycles
              if (visitedNodes.has(current)) {
                console.error(`Cycle detected in hierarchy at node: ${current}`);
                break;
              }
              visitedNodes.add(current);

              const parent = parentChildMap.get(current);
              if (!parent) break;

              pathNodeIds.add(parent);
              current = parent;
            }

            // Also find any process nodes that connect nodes in the path
            const processNodeIds = new Set<string>();
            if (pathNodeIds.size > 0) {
              // Include the source and destination in the path array for process connections
              const pathArray = [...Array.from(pathNodeIds), source, destination];

              const { docs: processNodes } = await req.payload.find({
                collection: 'flowchart',
                where: {
                  and: [
                    { type: { equals: 'Process' } },
                    {
                      or: [
                        // Find processes that connect nodes in the path
                        {
                          and: [
                            { processFrom: { in: pathArray } },
                            { processTo: { in: pathArray } },
                          ],
                        },
                      ],
                    },
                  ],
                },
                pagination: false,
              });

              processNodes.forEach((node: any) => {
                processNodeIds.add(node.id);
              });
            }

            return [...pathNodeIds, ...processNodeIds];
          };

          // Parallelize queries for initial nodes
          const [sourceDestNodes, { docs: directlyConnectedNodes }] = await Promise.all([
            getNodesWithHierarchy([source as string, destination as string]),
            req.payload.find({
              collection: 'flowchart',
              where: {
                or: [
                  {
                    and: [
                      { type: { equals: 'Innovation' } },
                      { 'innovationclass.processFrom': { equals: source } },
                      { 'innovationclass.processTo': { equals: destination } },
                    ],
                  },
                  { parent: { in: [source, destination] } },
                  { processFrom: { in: [source, destination] } },
                  { processTo: { in: [source, destination] } },
                ],
              },
              pagination: false,
              depth: 2,
            }),
          ]);

          const sourceNode = sourceDestNodes[source];
          const destNode = sourceDestNodes[destination];

          if (!sourceNode || !destNode) {
            return Response.json({ error: 'Nodes not found' }, { status: 404 });
          }

          // Find intermediate nodes in the path
          const intermediateNodeIds = await findIntermediateNodeIds(
            source as string,
            destination as string,
          );

          // If we found intermediate nodes, fetch their full data
          let intermediateNodes: any[] = [];
          if (intermediateNodeIds.length > 0) {
            const { docs: fetchedIntermediateNodes } = await req.payload.find({
              collection: 'flowchart',
              where: { id: { in: intermediateNodeIds } },
              depth: 2,
            });
            intermediateNodes = fetchedIntermediateNodes;
          }

          // Combine all nodes
          const nodes = new Map();
          nodes.set(sourceNode.id, sourceNode);
          nodes.set(destNode.id, destNode);

          // Add directly connected nodes
          for (const node of directlyConnectedNodes) {
            nodes.set(node.id, formatNode(node));
          }

          // Add intermediate nodes
          for (const node of intermediateNodes) {
            nodes.set(node.id, formatNode(node));
          }

          return Response.json({ docs: Array.from(nodes.values()) }, { status: 200 });
        } catch (error) {
          return Response.json({ error: error || 'Internal Server Error' }, { status: 500 });
        }
      },
    },
  ],
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
    },
    {
      name: 'type',
      type: 'select',
      options: [
        { label: 'Stage', value: 'Stage' },
        { label: 'Process', value: 'Process' },
        { label: 'Innovation Class', value: 'Innovation' },
      ],
      required: true,
    },
    {
      name: 'parent',
      type: 'relationship',
      relationTo: 'flowchart',
      hasMany: false,
      admin: {
        condition: (data) => data?.type === 'Stage', // Only show for Stage
      },
      hooks: {
        afterChange: [
          ({ value }) => {
            return value === null ? 'Root' : value; // Store Root as null
          },
        ],
      },
    },
    {
      name: 'innovationclass',
      type: 'relationship',
      relationTo: 'innovationClass',
      hasMany: false,
      admin: {
        condition: (data) => data?.type === 'Innovation', // Only show for Stage
      },
    },
    // {
    //   name: "processName",
    //   label:"Process name (optional)",
    //   type: "text",
    //   required:false,
    //   admin: {
    //     condition: (data) => data?.type === "Stage" && data.parent, // Only show if type is Stage
    //     hideAPIURL: true,
    // },
    {
      name: 'processFrom',
      type: 'relationship',
      relationTo: 'flowchart',
      hasMany: false,
      admin: {
        condition: (data) => data?.type === 'Process', // Only show for Process
      },
    },
    {
      name: 'processTo',
      type: 'relationship',
      relationTo: 'flowchart',
      hasMany: false,
      admin: {
        condition: (data) => data?.type === 'Process', // Only show for Process
      },
    },
  ],
};

export default Flowchart;
