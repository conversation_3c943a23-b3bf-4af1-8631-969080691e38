import type { CollectionConfig } from 'payload';

// Helper function to update innovation class sunburst status
async function updateInnovationClassSunburstStatus(
  payload: any,
  innovationClass: any,
  context?: any,
) {
  try {
    const innovationClassId = innovationClass?.innovationClass;
    // Check if this innovation class is linked to any sunburst items

    const isLinkedToSunburst = Boolean(innovationClassId);
    console.log({ innovationClassId, innovationClass });

    // Update the innovation class with the current linkage status
    await payload.update({
      collection: 'innovationClass',
      id: innovationClassId,
      data: { isAddedInSunburst: isLinkedToSunburst } as any,
    });

    console.log(
      `Updated innovation class ${innovationClassId} - isAddedInSunburst: ${isLinkedToSunburst}`,
    );
  } catch (error) {
    console.error(
      `Error updating innovation class sunburst status for ${innovationClass?.innovationClass}:`,
      error,
    );
  }
}

export const Sunburst: CollectionConfig = {
  slug: 'sunburst',
  access: {
    read: () => true,
  },
  admin: {
    useAsTitle: 'title',
    description: 'Data for sunburst visualizations',
    group: 'Visualization Data',
    hideAPIURL: true,
  },
  endpoints: [
    {
      path: '/sunburst-data',
      method: 'get',
      handler: async (req) => {
        try {
          const { payload } = req;

          // Fetch all sunburst items (optimized query with selected fields)
          const sunburstItems = await payload.find({
            collection: 'sunburst',
            depth: 1, // Reduced depth to avoid unnecessary data
            limit: 0,
          });

          // Fetch all innovation classes in one go
          const innovationClasses = await payload.find({
            collection: 'innovationClass',
            depth: 1,
            limit: 0,
          });

          // Convert innovation classes to a map for fast lookup
          const innovationClassMap = new Map(
            innovationClasses.docs.map((ic) => [ic.id, ic.innovations?.length || 0]),
          );

          // Find the root node
          const rootItem = sunburstItems.docs.find((item) => !item.parent);
          if (!rootItem) throw new Error('No root node found in the sunburst collection.');
          if (sunburstItems.docs.filter((item) => !item.parent).length > 1)
            throw new Error('Multiple root nodes found; expected exactly one.');

          // Build hierarchy iteratively (BFS approach using queue)
          const queue: any = [
            { id: rootItem.id, node: { name: rootItem.title, type: 'node', children: [] } },
          ];
          const hierarchyMap = new Map([[rootItem.id, queue[0].node]]);

          while (queue.length > 0) {
            const { id, node } = queue.shift();
            const children: any = sunburstItems.docs.filter((item: any) => item.parent?.id === id);

            for (const child of children) {
              const childNode = {
                name: child.title,
                type: child.innovation || child.innovationClass ? 'innovation' : 'node',
                innovationCount: 0,
                children: [],
              } as any;

              if (child.innovationClass) {
                // Get the innovation class details
                const innovationClass: any = innovationClasses.docs.find(
                  (ic) => ic.id === child.innovationClass.id,
                );

                childNode.type = 'class';
                childNode.slug = innovationClass?.slug || '';
                childNode.innovationCount = innovationClassMap.get(child.innovationClass.id) || 0;
                childNode.value = childNode.innovationCount;
              } else if (child.innovation) {
                childNode.innovationCount = 1;
              }

              node.children.push(childNode);
              hierarchyMap.set(child.id, childNode);
              queue.push({ id: child.id, node: childNode });
            }
          }

          // Compute total innovationCount
          const computeInnovationCount = (node: any) => {
            if (node.children.length === 0) {
              // Leaf node: assign value based on innovationCount
              node.value = node.innovationCount || 1; // Default to 1 if no innovationCount
              return node.innovationCount;
            }

            // Parent node: aggregate innovationCount from children
            node.innovationCount = node.children.reduce(
              (sum: any, child: any) => sum + computeInnovationCount(child),
              0,
            );
            return node.innovationCount;
          };

          computeInnovationCount(hierarchyMap.get(rootItem.id));

          return new Response(JSON.stringify(hierarchyMap.get(rootItem.id)), {
            status: 200,
            headers: { 'Content-Type': 'application/json' },
          });
        } catch (error) {
          console.error('Error generating sunburst data:', error);
          return new Response(
            JSON.stringify({ error: error || 'Failed to generate sunburst data' }),
            {
              status: 500,
              headers: { 'Content-Type': 'application/json' },
            },
          );
        }
      },
    },
  ],
  fields: [
    {
      name: 'parent',
      type: 'relationship',
      relationTo: 'sunburst',
      hasMany: false,
      label: 'Parent',
    },
    {
      name: 'innovation',
      type: 'relationship',
      label: 'Innovation',
      relationTo: 'innovations',
      admin: {
        description: 'Link this sunburst node to an innovation',
      },
    },
    {
      name: 'innovationDetails',
      type: 'group',
      admin: {
        description: 'Additional details about the linked innovation',
        condition: (data) => !!data.innovation,
      },
      fields: [
        {
          name: 'displayDetails',
          type: 'checkbox',
          defaultValue: true,
          admin: {
            description:
              'Whether to display detailed information about this innovation in the sunburst',
          },
        },
        {
          name: 'customLabel',
          type: 'text',
          admin: {
            description:
              'Custom label to display in the sunburst (leave empty to use innovation title)',
          },
        },
        {
          name: 'customDescription',
          type: 'textarea',
          admin: {
            description: 'Custom description to display in the sunburst tooltip',
          },
        },
      ],
    },
    {
      name: 'innovationClass',
      type: 'relationship',
      label: 'InnovationClass',
      relationTo: 'innovationClass',
      filterOptions: async ({ req }) => {
        const existingNodes = await req?.payload.find({
          collection: 'sunburst',
          where: {
            innovationClass: { exists: true },
          },
          depth: 0,
          pagination: false,
        });
        const usedClassIds = existingNodes.docs.map((node) => node.innovationClass);
        return {
          id: { not_in: usedClassIds },
        };
      },
    },
    {
      name: 'title',
      type: 'text',
      required: false, // No longer required since it will be set dynamically
      label: 'Title',
      admin: {
        condition: (data) => !data.innovation && !data.innovationClass, // Only show if no innovation or class is linked
      },
    },
  ],
  hooks: {
    beforeChange: [
      async ({ data, req }) => {
        const { payload } = req;

        // Prevent dual relationships
        if (data.innovation && data.innovationClass) {
          throw new Error('Node cannot link to both Innovation and Innovation Class');
        }

        // Handle Innovation Class
        if (data.innovationClass) {
          const innovationClass = await payload.findByID({
            collection: 'innovationClass',
            id: data.innovationClass,
          });

          data.title = innovationClass.name;

          // Check for existing usage
          const existingClass = await payload.find({
            collection: 'sunburst',
            where: { innovationClass: { equals: data.innovationClass } },
          });

          if (existingClass.docs.length > 0) {
            throw new Error('This innovation class is already in use');
          }
        }

        // Existing innovation handling
        if (data.innovation) {
          const innovation = await payload.findByID({
            collection: 'innovations',
            id: data.innovation,
            depth: 0,
          });

          // Set title from innovation if not explicitly provided
          if (!data.title) {
            data.title = innovation.title;
          }

          // Check if this innovation is already linked to another sunburst node
          const existingInnovation = await payload.find({
            collection: 'sunburst',
            where: {
              innovation: { equals: data.innovation },
              id: { not_equals: data.id }, // Exclude current node for updates
            },
          });

          if (existingInnovation.docs.length > 0) {
            throw new Error('This innovation is already linked to another sunburst node');
          }
        }

        // Validate title
        if (!data.title && !data.innovation && !data.innovationClass) {
          throw new Error('Title is required for non-linked nodes');
        }

        return data;
      },
    ],
    afterChange: [
      async ({ doc, req, previousDoc }) => {
        const { payload } = req;

        // Skip if we're in a skip context to prevent infinite loops
        if (req.context && req.context.skipSunburstHooks) {
          return;
        }

        try {
          // Handle innovation class linkage updates
          if (doc.innovationClass) {
            await updateInnovationClassSunburstStatus(payload, doc, req.context);
          }

          // Handle previous innovation class if it changed
          if (previousDoc?.innovationClass && previousDoc.innovationClass !== doc.innovationClass) {
            await updateInnovationClassSunburstStatus(payload, previousDoc, req.context);
          }

          // Handle innovation linkage (existing functionality)
          if (doc.innovation) {
            // Get the current innovation data
            const innovation = await payload.findByID({
              collection: 'innovations',
              id: doc.innovation,
              depth: 0,
            });

            // Update the innovation to link back to this sunburst node
            await payload.update({
              collection: 'innovations',
              id: doc.innovation,
              data: {
                // Cast to any to bypass TypeScript errors since we know the field exists
                ...(innovation as any),
                sunburstNode: doc.id,
              },
              // Use context to prevent infinite loops
              context: { skipSunburstHooks: true },
            });

            console.log(`Updated innovation ${doc.innovation} to link to sunburst node ${doc.id}`);
          }
        } catch (error) {
          console.error(`Error in sunburst afterChange hook:`, error);
        }
      },
    ],
    afterDelete: [
      async ({ doc, req }) => {
        const { payload } = req;

        try {
          // Handle innovation class linkage updates when sunburst item is deleted
          if (doc.innovationClass) {
            await updateInnovationClassSunburstStatus(payload, doc.innovationClass, req.context);
          }

          // Handle innovation linkage (existing functionality)
          if (doc.innovation) {
            // Get the current innovation data
            const innovation = await payload.findByID({
              collection: 'innovations',
              id: doc.innovation,
              depth: 0,
            });

            // Create a copy of the innovation data without the sunburstNode field
            const updatedData = { ...(innovation as any) };
            updatedData.sunburstNode = null;

            // Update the innovation to remove the link to this sunburst node
            await payload.update({
              collection: 'innovations',
              id: doc.innovation,
              data: updatedData,
              context: { skipSunburstHooks: true },
            });

            console.log(`Removed sunburst link from innovation ${doc.innovation}`);
          }
        } catch (error) {
          console.error(`Error in sunburst afterDelete hook:`, error);
        }
      },
    ],
  },
};
