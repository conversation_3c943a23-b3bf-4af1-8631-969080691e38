import RichTextEditor from '@/admin/components/RichTextEditor';
import { CollectionConfig } from 'payload';

async function documentExists(collection: string, id: string, payload: any): Promise<boolean> {
  try {
    await payload.findByID({
      collection,
      id,
      depth: 0,
    });
    return true;
  } catch (error) {
    if (error === 404) return false;
    throw error;
  }
}

const InnovationClass: CollectionConfig = {
  slug: 'innovationClass',
  admin: {
    useAsTitle: 'name',
    description: 'Categories/classifications for innovations.',
    group: 'Innovation Management',
    hideAPIURL: true,
  },
  labels: {
    singular: 'Innovation Class',
    plural: 'Innovation Class',
  },
  access: {
    read: () => true,
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
    },
    {
      name: 'slug',
      type: 'text',
      unique: true,
      required: true,
      admin: {
        position: 'sidebar',
        hidden: true,
      },
      hooks: {
        beforeValidate: [
          ({ siblingData }) => {
            if (!siblingData.slug && siblingData.name) {
              return siblingData.name
                .toLowerCase()
                .replace(/[^a-z0-9]+/g, '-')
                .replace(/(^-|-$)/g, '');
            }
          },
        ],
      },
    },
    {
      name: 'description',
      type: 'textarea',
      admin: {
        hidden: true,
      },
    },
    {
      name: 'descriptionEditor',
      type: 'ui',
      admin: {
        components: {
          Field: RichTextEditor as any,
        },
      },
      label: 'Description',
    },
    {
      name: 'processFrom',
      type: 'relationship',
      relationTo: 'flowchart',
      hasMany: false,
      required: true,
    },
    {
      name: 'processTo',
      type: 'relationship',
      relationTo: 'flowchart',
      hasMany: false,
      required: false,
    },
    {
      name: 'innovations',
      label: 'Linked Innovations',
      type: 'relationship',
      relationTo: 'innovations',
      hasMany: true,
      // Filter in admin UI for convenience, but hooks enforce logic
      filterOptions: {
        status: { equals: 'approved' },
      },
      admin: {
        description:
          'Innovations are automatically added/removed here based on their status and assigned class.',
      },
    },
    {
      name: 'isAddedInSunburst',
      label: 'Used in Sunburst',
      type: 'checkbox',
      defaultValue: false,
      admin: {
        description:
          'Automatically indicates if this innovation class is linked with any sunburst table item. This field is automatically managed.',
        position: 'sidebar',
        readOnly: true,
      },
    },
  ],
  hooks: {
    afterChange: [
      async ({ doc, req, operation, previousDoc }) => {
        const { payload } = req;
        const context = { disableHooks: true };

        try {
          // Handle flowchart
          if (operation === 'create') {
            await payload.create({
              collection: 'flowchart',
              data: {
                name: doc?.name,
                type: 'Innovation',
                innovationclass: doc?.id,
              },
              context,
            });
          }

          // Handle innovations relationships
          if (operation === 'update') {
            const prevIds = previousDoc?.innovations?.map((i: any) => i.toString()) || [];
            const currentIds = doc?.innovations?.map((i: any) => i.toString()) || [];

            if (JSON.stringify(prevIds) !== JSON.stringify(currentIds)) {
              const added = currentIds.filter((id: any) => !prevIds.includes(id));
              const removed = prevIds.filter((id: any) => !currentIds.includes(id));

              // Process additions
              for (const innovationId of added) {
                if (await documentExists('innovations', innovationId, payload)) {
                  await payload.update({
                    collection: 'innovations',
                    id: innovationId,
                    data: { innovationClass: doc?.id },
                    context,
                  });
                }
              }

              // Process removals
              for (const innovationId of removed) {
                if (await documentExists('innovations', innovationId, payload)) {
                  await payload.update({
                    collection: 'innovations',
                    id: innovationId,
                    data: { innovationClass: null },
                    context,
                  });
                }
              }
            }
          }
        } catch (error) {
          console.error('InnovationClass afterChange error:', error);
          payload.logger.error(`InnovationClass sync failed: ${error}`);
        }
      },
    ],

    afterDelete: [
      async ({ doc, req }) => {
        const { payload } = req;
        const context = { disableHooks: true };

        try {
          // Cleanup flowchart
          await payload.delete({
            collection: 'flowchart',
            where: { innovationclass: { equals: doc?.id } },
          });

          // Clear relationships
          if (doc?.innovations?.length) {
            for (const innovationId of doc?.innovations) {
              if (await documentExists('innovations', innovationId.toString(), payload)) {
                await payload.update({
                  collection: 'innovations',
                  id: innovationId.toString(),
                  data: { innovationClass: null },
                  context,
                });
              }
            }
          }
        } catch (error) {
          console.error('InnovationClass delete cleanup error:', error);
        }
      },
    ],
  },
  endpoints: [
    {
      path: '/getOptions',
      method: 'get',
      handler: async (req) => {
        try {
          const { payload } = req;
          const response: any = await payload.find({
            collection: 'innovationClass',
            pagination: false,
            depth: 0,
          });
          const formatedResponse = response?.docs?.map((item: any) => {
            return {
              InnovationClass: item.id,
              value: item.name,
              slug: item.slug,
              processFrom: item.processFrom,
              processTo: item.processTo,
              isAddedInSunburst: item.isAddedInSunburst, // Include the sunburst linkage info
            };
          });

          return new Response(JSON.stringify(formatedResponse), {
            status: 200,
            headers: { 'Content-Type': 'application/json' },
          });
        } catch (error) {
          return Response.json({ error: error }, { status: 500 });
        }
      },
    },
    {
      path: '/innovations',
      method: 'get',
      handler: async (req: any) => {
        try {
          const { searchParams } = new URL(req.url);

          // Parallelize all parameter parsing
          const [page, limit, searchQuery, innovationClass] = await Promise.all([
            parseInt(searchParams.get('page') || '1', 10),
            parseInt(searchParams.get('limit') || '10', 10),
            searchParams.get('searchQuery') || '',
            searchParams.get('innovationClass') || '',
          ]);

          // Try to find by name or slug
          const innovationClasses = await req.payload.find({
            collection: 'innovationClass',
            where: {
              or: [{ name: { equals: innovationClass } }, { slug: { equals: innovationClass } }],
            },
            depth: 0, // ✅ Fetch only required data
          });

          if (!innovationClasses.docs.length) {
            return new Response(JSON.stringify({ error: 'Innovation class not found' }), {
              status: 404,
              headers: { 'Content-Type': 'application/json' },
            });
          }

          const innovationClassId = innovationClasses.docs[0].id; // ✅ Get the class ID

          // Build the search filter
          const searchFilter = searchQuery
            ? {
                or: [
                  { title: { contains: searchQuery, mode: 'insensitive' } }, // ✅ Search in title
                  { description: { contains: searchQuery, mode: 'insensitive' } }, // ✅ Search in description
                ],
              }
            : {}; // No search filter if query is empty

          // Fetch innovations that belong to this innovation class & match search query
          const innovations = await req.payload.find({
            collection: 'innovations',
            where: {
              innovationClass: { equals: innovationClassId }, // ✅ Filter by class ID
              status: { equals: 'approved' },
              ...searchFilter, // ✅ Apply search if provided
            },
            limit,
            page,
            depth: 0, // ✅ Reduce populated relationships for performance
          });

          // Format response
          const formattedInnovations = {
            docs: innovations.docs.map((doc: any) => ({
              id: doc?.id,
              title: doc?.title,
              description: doc?.description,
              link: doc?.link,
              organizationName: doc?.organizationName,
              status: doc?.status,
              slug: doc?.slug,
              createdAt: doc?.createdAt, // Optional: Include timestamps if needed
              updatedAt: doc?.updatedAt,
            })),
            totalDocs: innovations.totalDocs,
            limit: innovations.limit,
            page: innovations.page,
            totalPages: innovations.totalPages,
            hasPrevPage: innovations.hasPrevPage,
            hasNextPage: innovations.hasNextPage,
            prevPage: innovations.prevPage,
            nextPage: innovations.nextPage,
          };

          return new Response(JSON.stringify(formattedInnovations), {
            status: 200,
            headers: {
              'Content-Type': 'application/json',
            },
          });
        } catch (error) {
          console.error('API Error:', error);
          return new Response(JSON.stringify({ error: 'Internal Server Error' }), {
            status: 500,
            headers: { 'Content-Type': 'application/json' },
          });
        }
      },
    },
  ],
};

export default InnovationClass;
